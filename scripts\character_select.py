import random
from PIL import Image
import gradio as gr
import modules.scripts as scripts
import json
import os
import shutil
import requests
import textwrap
import traceback
from pathlib import Path


#  *********     versioning     *****
repo = "a1111"

try:
    import launch

    if not launch.is_installed("colorama"):
            launch.run_pip("install colorama")
except:
    pass

try:
    from colorama import just_fix_windows_console
    just_fix_windows_console()
except:
    pass


class CharacterSelect(scripts.Script):
    """
    Simplified Character Select Script

    This script now uses a simplified approach where:
    1. UI components only store selections without modifying the prompt box
    2. The final prompt is generated in the process() method during image generation
    3. This eliminates complex prompt manipulation and string replacement logic
    """

    BASEDIR = scripts.basedir()

    def __init__(self, *args, **kwargs):

        self.settings_file = "settings.json"
        self.character_file = "character.json"
        self.action_file = "action.json"
        self.custom_settings_file = "custom_settings.json"
        self.custom_character_file = "custom_character.json"
        self.custom_action_file = "custom_action.json"

        # Read saved settings
        self.settings = self.get_config2(self.settings_file)

        self.copy_json_file(self.settings_file,self.custom_settings_file)
        self.copy_json_file(self.character_file,self.custom_character_file)
        self.copy_json_file(self.action_file,self.custom_action_file)

        try:
            self.settings = self.get_config2(self.custom_settings_file)
        except:
            print(f"错误：自订设定 '{self.custom_settings_file}' 不存在")

        # Load combined character configuration
        self.hm_config_1_img = {}
        self.hm_config_1_component = {}

        for item in self.get_config2("assets.json"):
            prompt = item["prompt"]
            display_name = item["display_name"]
            image_path = item["image_path"]

            # Add to components - use display_name as key for dropdown, but map to prompt
            self.hm_config_1_img[display_name] = image_path
            self.hm_config_1_component[display_name] = prompt

        # Ensure the component keys are sorted
        self.hm_config_1_component = {k: self.hm_config_1_component[k] for k in sorted(self.hm_config_1_component.keys())}

        self.hm_config_2_component = self.get_config2("custom_action.json")

        self.loading = False

        # Current selections for simplified prompt generation
        self.current_character = ""
        self.current_action = ""
        self.current_ai_prompt = ""
        self.current_func_states = set()  # Set of enabled functional features

        self.elm_prfx = "characterselect"
        CharacterSelect.txt2img_neg_prompt_btn = gr.Button(
            value="使用预设",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_neg_prompt_btn"
        )

        CharacterSelect.random_character_button = gr.Button(
            value="随机",
            variant="primary",
            render = False,
            scale=0,
            min_width=80,
            elem_id=f"{self.elm_prfx}_C_randomprompt_btn"
        )
        CharacterSelect.random_action_button = gr.Button(
            value="随机",
            variant="primary",
            render = False,
            scale=0,
            min_width=80,
            elem_id=f"{self.elm_prfx}_A_randomprompt_btn"
        )


        #h_m 人物
        CharacterSelect.character_dropdown = gr.Dropdown(
            choices=['', 'random'] + list(self.hm_config_1_component.keys()),
            show_label=False,
            render = False,
            elem_id=f"{self.elm_prfx}_hm1_dd"
        )

        CharacterSelect.txt2img_hm1_img = gr.Image(
            width=100,
            container=True
        )

        #h_m 姿势
        CharacterSelect.action_dropdown = gr.Dropdown(
            choices=['', 'random'] + list(self.hm_config_2_component.keys()),
            show_label=False,
            render = False,
            elem_id=f"{self.elm_prfx}_hm2_dd"
        )

        #功能性调节
        CharacterSelect.func00_chk =gr.Checkbox(
            label="NSFW",
            value=False,
            render = False,
            container = False,
            scale=0, min_width=80,
            elem_id=f"{self.elm_prfx}_func00_chk"
        )
        CharacterSelect.func01_chk =gr.Checkbox(
            label="增加细节",
            render = False,
            container = False,
            scale=0, min_width=80,
            elem_id=f"{self.elm_prfx}_func01_chk"
        )
        CharacterSelect.func02_chk =gr.Checkbox(
            label="身材加强",
            render = False,
            container = False,
            scale=0, min_width=80,
            elem_id=f"{self.elm_prfx}_func02_chk"
        )
        CharacterSelect.func03_chk = gr.Checkbox(
            label="品质加强",
            render = False,
            container = False,
            scale=0, min_width=80,
            elem_id=f"{self.elm_prfx}_func03_chk"
        )
        CharacterSelect.func04_chk =gr.Checkbox(
            label="人物加强",
            render = False,
            container = False,
            scale=0, min_width=80,
            elem_id=f"{self.elm_prfx}_func04_chk"
        )


        #中文输入框
        CharacterSelect.txt2img_cprompt_txt = gr.Textbox(lines=4, placeholder="可输入中文描述，透过AI扩充场景", label="AI 扩充 prompt", elem_id=f"{self.elm_prfx}_cprompt_txt")
        CharacterSelect.txt2img_cprompt_btn = gr.Button(
            value="AI扩充",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_cprompt_btn"
        )

        self.input_prompt = CharacterSelect.txt2img_cprompt_txt

    def title(self):
        return "CharacterSelect"

    def show(self, is_img2img):
        return scripts.AlwaysVisible

    def _before_component(self):
        with gr.Accordion(label="人物动作设定", open=True):
            with gr.Row():
                CharacterSelect.character_dropdown.render()
                CharacterSelect.random_character_button.render()

            with gr.Row():
                with gr.Column():
                    with gr.Row():
                        CharacterSelect.action_dropdown.render()
                        CharacterSelect.random_action_button.render()
                    with gr.Row():
                        CharacterSelect.func00_chk.render()
                        CharacterSelect.func01_chk.render()
                        CharacterSelect.func02_chk.render()
                        CharacterSelect.func03_chk.render()
                        CharacterSelect.func04_chk.render()
                    with gr.Row():
                        CharacterSelect.txt2img_neg_prompt_btn.render()
                CharacterSelect.txt2img_hm1_img.render()

            if(self.settings["ai"]):
                with gr.Row():
                    CharacterSelect.txt2img_cprompt_txt.render()
                    CharacterSelect.txt2img_cprompt_btn.render()



    def after_component(self, component, **kwargs):
        ele = kwargs.get("elem_id")

        # 提示词
        if ele == "txt2img_prompt":
            self.prompt_component = component
        if ele == "txt2img_neg_prompt":
            self.neg_prompt_component = component
        if ele == "txt2img_steps":
            self.steps_component = component
        if ele == "txt2img_height":
            self.height_component = component
        if ele == "txt2img_width":
            self.width_component = component


        if ele == "txt2img_generation_info_button" or ele == "img2img_generation_info_button":
            self._ui()

        # if ele == "txt2img_styles_dialog":
        if ele == "txt2img_prompt_container":
            self._before_component()

    def ui(self, *args):
        pass

    def _ui(self):
        # Conditional for class members
        if self.is_txt2img:
            CharacterSelect.txt2img_neg_prompt_btn.click(
                fn=self.fetch_neg_prompt,
                outputs=[self.neg_prompt_component,self.steps_component,self.height_component,self.width_component,self.func00_chk,self.func01_chk,self.func02_chk,self.func03_chk,self.func04_chk]
            )
            #hm
            CharacterSelect.character_dropdown.change(
                fn=self.hm1_setting,
                inputs=[CharacterSelect.character_dropdown,self.prompt_component],
                outputs=[CharacterSelect.txt2img_hm1_img, self.prompt_component]
            )

            CharacterSelect.action_dropdown.change(
                fn=self.hm2_setting,
                inputs=[CharacterSelect.action_dropdown, self.prompt_component],
                outputs=[CharacterSelect.action_dropdown, self.prompt_component]
            )

            #细节功能 - Checkboxes no longer update main prompt
            detailinput = [self.prompt_component,CharacterSelect.func00_chk,CharacterSelect.func01_chk,CharacterSelect.func02_chk,CharacterSelect.func03_chk,CharacterSelect.func04_chk]
            CharacterSelect.func00_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=[]
            )
            CharacterSelect.func01_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=[]
            )
            CharacterSelect.func02_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=[]
            )
            CharacterSelect.func03_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=[]
            )
            CharacterSelect.func04_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=[]
            )

            CharacterSelect.random_character_button.click(
                fn=self.on_random_character,
                inputs=self.prompt_component,
                outputs=[self.prompt_component, CharacterSelect.character_dropdown]
            )
            CharacterSelect.random_action_button.click(
                fn=self.on_random_action,
                inputs=self.prompt_component,
                outputs=[self.prompt_component,CharacterSelect.action_dropdown]
            )

            CharacterSelect.txt2img_cprompt_btn.click(
                fn=self.cprompt_send,
                inputs=[self.prompt_component, self.input_prompt],
                outputs=self.prompt_component
            )

    def process(self, p, *args):
        p.all_prompts = [self.generate_final_prompt(prompt) for prompt in p.all_prompts]

    def generate_final_prompt(self, original_prompt=""):
        """Generate the final prompt based on current selections without modifying UI components."""
        prompt_parts = []

        # Add original prompt as-is (preserve user input)
        if original_prompt:
            prompt_parts.append(original_prompt.rstrip(', \n'))

        # Add character prompt if selected
        if self.current_character:
            character = random.choice(list(self.hm_config_1_component.keys())) if self.current_character == "random" else self.current_character
            character_prompt = self.hm_config_1_component.get(character, "")
            if character_prompt:
                prompt_parts.append(character_prompt)

        # Add action prompt if selected
        if self.current_action:
            action = random.choice(list(self.hm_config_2_component.keys())) if self.current_action == "random" else self.current_action
            action_prompt = self.hm_config_2_component.get(action, "")
            if action_prompt:
                prompt_parts.append(action_prompt)

        # Add functional prompts based on checkbox states
        func_prompts = self.generate_functional_prompts()
        if func_prompts:
            prompt_parts.append(func_prompts)

        # Add AI-generated prompt if exists
        if self.current_ai_prompt:
            prompt_parts.append(self.current_ai_prompt)

        return ',\n'.join(prompt_parts)

    def generate_functional_prompts(self):
        """Generate functional prompts based on current checkbox states."""
        func_parts = []
        for func_name in self.current_func_states:
            prompt = self.settings.get(func_name, "")
            if prompt:
                func_parts.append(prompt)
        return ",\n".join(func_parts)

    def get_config2(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode, encoding='utf-8') as f:
                as_dict = json.load(f)
        except FileNotFoundError as e:
            print(f"{e}\n{file} not found, check if it exists or if you have moved it.")
        return as_dict

    def fetch_neg_prompt(self):
        self.neg_prompt_component.value = self.settings["neg_prompt"]
        self.steps_component.value = self.settings["steps"]
        self.height_component.value = self.settings["height"]
        self.width_component.value = self.settings["width"]

        self.current_func_states = {'more_detail', 'chihunhentai', 'quality', 'character_enhance'}
        return [self.neg_prompt_component.value,self.steps_component.value,self.height_component.value,self.width_component.value,False,True,True,True,True]

    def on_random_character(self, oldprompt):
        return self.hm1_setting("random", oldprompt)

    def on_random_action(self, oldprompt):
        self.hm2_setting("random", oldprompt)
        return [oldprompt, "random"]

    def hm1_setting(self, selection, oldprompt):
        if self.loading:
            return
        self.loading = True
        try:
            self.current_character = selection

            if selection == "random":
                return [Image.new('RGB', (100, 100), color='lightgray'), oldprompt]
            elif selection and selection in self.hm_config_1_img:
                filepath = os.path.join(CharacterSelect.BASEDIR, self.hm_config_1_img[selection])
                try:
                    image = self.load_image_from_path(filepath)
                    return [image, oldprompt]
                except Exception as e:
                    print(f"Error loading image: {e}")
                    return [Image.new('RGB', (100, 100), color='gray'), oldprompt]
            else:
                return [Image.new('RGB', (100, 100), color='gray'), oldprompt]
        except:
            traceback.print_exc()
            return [Image.new('RGB', (100, 100), color='gray'), oldprompt]
        finally:
            self.loading = False

    def hm2_setting(self, selection, oldprompt):
        self.current_action = selection
        return [selection, oldprompt]

    def func_setting(self, _oldprompt, fv0, fv1, fv2, fv3, fv4):
        func_mapping = [
            ('nsfw', fv0),
            ('more_detail', fv1),
            ('chihunhentai', fv2),
            ('quality', fv3),
            ('character_enhance', fv4)
        ]
        self.current_func_states = {name for name, enabled in func_mapping if enabled}

    def cprompt_send(self, oldprompt, input_prompt):
        generated_texts = self.send_request(input_prompt)
        self.current_ai_prompt = ""

        if isinstance(generated_texts, str):
            self.current_ai_prompt = generated_texts
        else:
            for text in generated_texts:
                self.current_ai_prompt += text

        self.current_ai_prompt = self.current_ai_prompt.replace(", ", ",")
        return oldprompt

    def send_request(self, input_prompt, **kwargs):
        prime_directive = textwrap.dedent("""\
            Act as a prompt maker with the following guidelines:
            - Break keywords by commas.
            - Provide high-quality, non-verbose, coherent, brief, concise, and not superfluous prompts.
            - Focus solely on the visual elements of the picture; avoid art commentaries or intentions.
            - Construct the prompt with the component format:
            1. Start with the subject and keyword description.
            2. Follow with motion keyword description.
            3. Follow with scene keyword description.
            4. Finish with background and keyword description.
            - Limit yourself to no more than 20 keywords per component
            - Include all the keywords from the user's request verbatim as the main subject of the response.
            - Be varied and creative.
            - Always reply on the same line and no more than 100 words long.
            - Do not enumerate or enunciate components.
            - Create creative additional information in the response.
            - Response in English.
            - Response prompt only.
            The followin is an illustartive example for you to see how to construct a prompt your prompts should follow this format but always coherent to the subject worldbuilding or setting and cosider the elemnts relationship.
            Example:
            Demon Hunter,Cyber City,A Demon Hunter,standing,lone figure,glow eyes,deep purple light,cybernetic exoskeleton,sleek,metallic,glowing blue accents,energy weapons,Fighting Demon,grotesque creature,twisted metal,glowing red eyes,sharp claws,towering structures,shrouded haze,shimmering energy,
            Make a prompt for the following Subject:
            """)
        data = {
                'model': self.settings["model"],
                'messages': [
                    {"role": "system", "content": prime_directive},
                    {"role": "user", "content": input_prompt + ";Response in English"}
                ],
            }
        headers = kwargs.get('headers', {"Content-Type": "application/json", "Authorization": "Bearer " + self.settings["api_key"]})
        base_url = self.settings["base_url"]
        response = requests.post(base_url, headers=headers, json=data)

        if response.status_code == 200:
            return response.json().get('choices', [{}])[0].get('message', {}).get('content', '')
        else:
            print(f"Error: Request failed with status code {response.status_code}")
            return []

    def load_image_from_path(self, image_path):
        try:
            return Image.open(image_path)
        except Exception as e:
            print(f"Error loading image: {e}")
            return Image.new('RGB', (100, 100), color='gray')

    def copy_json_file(self, source_path: str, destination_path: str, overwrite: bool = False):
        """
        复制JSON档案并确认其格式正确
        Parameters:
        source_path (str): 来源JSON档案的路径
        destination_path (str): 目标位置的路径
        overwrite (bool): 若为True则覆写已存在的档案，预设为False
        Returns:
        bool: 复制成功返回True，失败返回False
        """
        try:
            # 确认来源档案存在
            file = Path(os.path.join(CharacterSelect.BASEDIR, source_path))
            if not file.exists():
                print(f"错误：来源档案 '{source_path}' 不存在")
                return False

            # 检查目标档案是否已存在
            dest = Path(os.path.join(CharacterSelect.BASEDIR, destination_path))
            if dest.exists() and not overwrite:
                return False

            # 复制档案
            shutil.copy2(os.path.join(CharacterSelect.BASEDIR, source_path), os.path.join(CharacterSelect.BASEDIR, destination_path))
            print(f"成功：档案已复制到 '{dest}'")
            return True

        except json.JSONDecodeError:
            print(f"错误：'{source_path}' 不是有效的JSON档案")
            return False
        except Exception as e:
            print(f"错误：复制过程发生问题 - {str(e)}")
            return False

